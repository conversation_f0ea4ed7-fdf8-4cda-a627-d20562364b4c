"""Initial product table

Revision ID: 0001
Revises: 
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pim_products',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, comment='Unique identifier for the product'),
    sa.Column('name', sa.String(length=255), nullable=False, comment='Product name'),
    sa.Column('sku', sa.String(length=100), nullable=False, comment='Stock Keeping Unit - unique product identifier'),
    sa.Column('description', sa.Text(), nullable=True, comment='Detailed product description'),
    sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=False, comment='Product price with 2 decimal places'),
    sa.Column('inventory_level', sa.Integer(), nullable=False, comment='Current inventory/stock level'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='Whether the product is active/available'),
    sa.Column('category', sa.String(length=100), nullable=True, comment='Product category'),
    sa.Column('brand', sa.String(length=100), nullable=True, comment='Product brand'),
    sa.Column('weight', sa.Numeric(precision=8, scale=3), nullable=True, comment='Product weight in kg'),
    sa.Column('dimensions', sa.String(length=100), nullable=True, comment='Product dimensions (L x W x H)'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Timestamp when the product was created'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Timestamp when the product was last updated'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('sku')
    )
    op.create_index(op.f('ix_pim_products_brand'), 'pim_products', ['brand'], unique=False)
    op.create_index(op.f('ix_pim_products_category'), 'pim_products', ['category'], unique=False)
    op.create_index(op.f('ix_pim_products_id'), 'pim_products', ['id'], unique=False)
    op.create_index(op.f('ix_pim_products_is_active'), 'pim_products', ['is_active'], unique=False)
    op.create_index(op.f('ix_pim_products_name'), 'pim_products', ['name'], unique=False)
    op.create_index(op.f('ix_pim_products_sku'), 'pim_products', ['sku'], unique=False)
    
    # Create trigger to automatically update updated_at timestamp
    op.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    op.execute("""
        CREATE TRIGGER update_pim_products_updated_at 
        BEFORE UPDATE ON pim_products 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    """)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DROP TRIGGER IF EXISTS update_pim_products_updated_at ON pim_products;")
    op.execute("DROP FUNCTION IF EXISTS update_updated_at_column();")
    
    op.drop_index(op.f('ix_pim_products_sku'), table_name='pim_products')
    op.drop_index(op.f('ix_pim_products_name'), table_name='pim_products')
    op.drop_index(op.f('ix_pim_products_is_active'), table_name='pim_products')
    op.drop_index(op.f('ix_pim_products_id'), table_name='pim_products')
    op.drop_index(op.f('ix_pim_products_category'), table_name='pim_products')
    op.drop_index(op.f('ix_pim_products_brand'), table_name='pim_products')
    op.drop_table('pim_products')
    # ### end Alembic commands ###
