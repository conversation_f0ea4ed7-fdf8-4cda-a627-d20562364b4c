"""
Pydantic schemas for product validation and serialization.
"""
from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class ProductBase(BaseModel):
    """Base product schema with common fields."""
    
    name: str = Field(..., min_length=1, max_length=255, description="Product name")
    sku: str = Field(..., min_length=1, max_length=100, description="Stock Keeping Unit")
    description: Optional[str] = Field(None, description="Product description")
    price: Decimal = Field(..., gt=0, decimal_places=2, description="Product price")
    inventory_level: int = Field(0, ge=0, description="Inventory level")
    is_active: bool = Field(True, description="Product active status")
    category: Optional[str] = Field(None, max_length=100, description="Product category")
    brand: Optional[str] = Field(None, max_length=100, description="Product brand")
    weight: Optional[Decimal] = Field(None, gt=0, decimal_places=3, description="Weight in kg")
    dimensions: Optional[str] = Field(None, max_length=100, description="Dimensions (L x W x H)")
    
    @validator("sku")
    def validate_sku(cls, v):
        """Validate SKU format."""
        if not v or not v.strip():
            raise ValueError("SKU cannot be empty")
        # Remove extra whitespace and convert to uppercase
        return v.strip().upper()
    
    @validator("name")
    def validate_name(cls, v):
        """Validate product name."""
        if not v or not v.strip():
            raise ValueError("Product name cannot be empty")
        return v.strip()
    
    @validator("price")
    def validate_price(cls, v):
        """Validate price is positive."""
        if v <= 0:
            raise ValueError("Price must be greater than 0")
        return v


class ProductCreate(ProductBase):
    """Schema for creating a new product."""
    pass


class ProductUpdate(BaseModel):
    """Schema for updating an existing product (partial update)."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    sku: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    inventory_level: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    category: Optional[str] = Field(None, max_length=100)
    brand: Optional[str] = Field(None, max_length=100)
    weight: Optional[Decimal] = Field(None, gt=0, decimal_places=3)
    dimensions: Optional[str] = Field(None, max_length=100)
    
    @validator("sku")
    def validate_sku(cls, v):
        """Validate SKU format."""
        if v is not None:
            if not v or not v.strip():
                raise ValueError("SKU cannot be empty")
            return v.strip().upper()
        return v
    
    @validator("name")
    def validate_name(cls, v):
        """Validate product name."""
        if v is not None:
            if not v or not v.strip():
                raise ValueError("Product name cannot be empty")
            return v.strip()
        return v
    
    @validator("price")
    def validate_price(cls, v):
        """Validate price is positive."""
        if v is not None and v <= 0:
            raise ValueError("Price must be greater than 0")
        return v


class ProductInDB(ProductBase):
    """Schema for product as stored in database."""
    
    id: UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """Pydantic config."""
        orm_mode = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: float(v),
        }


class ProductResponse(ProductInDB):
    """Schema for product API responses."""
    pass


class ProductListResponse(BaseModel):
    """Schema for paginated product list responses."""
    
    items: list[ProductResponse]
    total: int
    page: int
    size: int
    pages: int
    
    class Config:
        """Pydantic config."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: float(v),
        }
