"""
Database initialization script.
"""
import sys
import os
from decimal import Decimal

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine, create_tables
from app.models.product import Product
from app.core.config import settings

def create_sample_products(db: Session) -> None:
    """Create sample products for testing."""
    
    sample_products = [
        {
            "name": "Wireless Bluetooth Headphones",
            "sku": "WBH-001",
            "description": "High-quality wireless Bluetooth headphones with noise cancellation",
            "price": Decimal("99.99"),
            "inventory_level": 50,
            "category": "Electronics",
            "brand": "TechSound",
            "weight": Decimal("0.250"),
            "dimensions": "20cm x 18cm x 8cm"
        },
        {
            "name": "Organic Cotton T-Shirt",
            "sku": "OCT-002",
            "description": "100% organic cotton t-shirt, available in multiple colors",
            "price": Decimal("24.99"),
            "inventory_level": 100,
            "category": "Clothing",
            "brand": "EcoWear",
            "weight": Decimal("0.150"),
            "dimensions": "Medium"
        },
        {
            "name": "Stainless Steel Water Bottle",
            "sku": "SSWB-003",
            "description": "Insulated stainless steel water bottle, keeps drinks cold for 24 hours",
            "price": Decimal("29.99"),
            "inventory_level": 75,
            "category": "Home & Kitchen",
            "brand": "HydroLife",
            "weight": Decimal("0.400"),
            "dimensions": "25cm x 7cm x 7cm"
        },
        {
            "name": "Yoga Mat Premium",
            "sku": "YMP-004",
            "description": "Premium non-slip yoga mat with carrying strap",
            "price": Decimal("49.99"),
            "inventory_level": 30,
            "category": "Sports & Fitness",
            "brand": "ZenFit",
            "weight": Decimal("1.200"),
            "dimensions": "183cm x 61cm x 0.6cm"
        },
        {
            "name": "LED Desk Lamp",
            "sku": "LDL-005",
            "description": "Adjustable LED desk lamp with USB charging port",
            "price": Decimal("39.99"),
            "inventory_level": 25,
            "category": "Home & Office",
            "brand": "BrightLight",
            "weight": Decimal("0.800"),
            "dimensions": "45cm x 15cm x 15cm"
        }
    ]
    
    for product_data in sample_products:
        # Check if product already exists
        existing_product = db.query(Product).filter(Product.sku == product_data["sku"]).first()
        if not existing_product:
            product = Product(**product_data)
            db.add(product)
    
    db.commit()
    print(f"Created {len(sample_products)} sample products")


def init_db() -> None:
    """Initialize the database."""
    print("Initializing database...")
    
    # Create tables
    create_tables()
    print("Database tables created successfully")
    
    # Create sample data
    db = SessionLocal()
    try:
        create_sample_products(db)
        print("Sample data created successfully")
    except Exception as e:
        print(f"Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()
    
    print("Database initialization completed!")


if __name__ == "__main__":
    print(f"Database URL: {settings.DATABASE_URL}")
    init_db()
