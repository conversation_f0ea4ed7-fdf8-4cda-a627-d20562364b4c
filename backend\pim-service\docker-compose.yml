version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: pim-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: pim_db
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    networks:
      - pim-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d pim_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PIM Service
  pim-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pim-service
    environment:
      - DATABASE_URL=********************************************/pim_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=pim_db
      - DEBUG=True
      - ENVIRONMENT=development
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - pim-network
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        sleep 10 &&
        echo 'Running database migrations...' &&
        alembic upgrade head &&
        echo 'Starting application...' &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pim-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - pim-network
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local

networks:
  pim-network:
    driver: bridge
