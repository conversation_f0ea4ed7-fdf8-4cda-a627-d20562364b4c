@echo off
setlocal enabledelayedexpansion

:menu
cls
echo ========================================
echo PIM Service Development Utilities
echo ========================================
echo.
echo 1. Start Development Server
echo 2. Run Database Migrations
echo 3. Create Sample Data
echo 4. Run API Tests
echo 5. Run Unit Tests
echo 6. Format Code (Black)
echo 7. Check Code Quality (Flake8)
echo 8. Start Docker Services
echo 9. Stop Docker Services
echo 10. View Logs
echo 11. Reset Database
echo 0. Exit
echo.
set /p choice="Enter your choice (0-11): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto run_migrations
if "%choice%"=="3" goto create_sample_data
if "%choice%"=="4" goto run_api_tests
if "%choice%"=="5" goto run_unit_tests
if "%choice%"=="6" goto format_code
if "%choice%"=="7" goto check_quality
if "%choice%"=="8" goto start_docker
if "%choice%"=="9" goto stop_docker
if "%choice%"=="10" goto view_logs
if "%choice%"=="11" goto reset_database
if "%choice%"=="0" goto exit
goto menu

:start_server
echo.
echo Starting development server...
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
pause
goto menu

:run_migrations
echo.
echo Running database migrations...
alembic upgrade head
if %errorlevel% equ 0 (
    echo Migrations completed successfully!
) else (
    echo Migration failed!
)
pause
goto menu

:create_sample_data
echo.
echo Creating sample data...
python scripts\init_db.py
if %errorlevel% equ 0 (
    echo Sample data created successfully!
) else (
    echo Failed to create sample data!
)
pause
goto menu

:run_api_tests
echo.
echo Running API tests...
python scripts\test_api.py
pause
goto menu

:run_unit_tests
echo.
echo Running unit tests...
pytest
if %errorlevel% equ 0 (
    echo All tests passed!
) else (
    echo Some tests failed!
)
pause
goto menu

:format_code
echo.
echo Formatting code with Black...
black app/ tests/ scripts/
if %errorlevel% equ 0 (
    echo Code formatted successfully!
) else (
    echo Code formatting failed!
)
pause
goto menu

:check_quality
echo.
echo Checking code quality with Flake8...
flake8 app/ tests/ scripts/
if %errorlevel% equ 0 (
    echo Code quality check passed!
) else (
    echo Code quality issues found!
)
pause
goto menu

:start_docker
echo.
echo Starting Docker services...
docker-compose up -d
if %errorlevel% equ 0 (
    echo Docker services started successfully!
    echo Waiting for services to be ready...
    timeout /t 10 /nobreak > nul
    echo Running migrations...
    docker-compose exec pim-service alembic upgrade head
) else (
    echo Failed to start Docker services!
)
pause
goto menu

:stop_docker
echo.
echo Stopping Docker services...
docker-compose down
if %errorlevel% equ 0 (
    echo Docker services stopped successfully!
) else (
    echo Failed to stop Docker services!
)
pause
goto menu

:view_logs
echo.
echo Viewing Docker logs...
echo Press Ctrl+C to stop viewing logs
docker-compose logs -f
pause
goto menu

:reset_database
echo.
echo WARNING: This will delete all data in the database!
set /p confirm="Are you sure you want to reset the database? (y/N): "
if /i "!confirm!"=="y" (
    echo Resetting database...
    alembic downgrade base
    alembic upgrade head
    echo Database reset completed!
    
    set /p sample="Do you want to create sample data? (y/N): "
    if /i "!sample!"=="y" (
        python scripts\init_db.py
        echo Sample data created!
    )
) else (
    echo Database reset cancelled.
)
pause
goto menu

:exit
echo.
echo Goodbye!
exit /b 0
