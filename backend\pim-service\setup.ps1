# PIM Service Setup Script for Windows PowerShell

Write-Host "========================================" -ForegroundColor Green
Write-Host "PIM Service Setup Script for Windows" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Step 1: Check Python installation
Write-Host "`nStep 1: Checking Python installation..." -ForegroundColor Yellow
if (-not (Test-Command "python")) {
    Write-Host "Python is not installed or not in PATH. Please install Python 3.11+ first." -ForegroundColor Red
    exit 1
}

$pythonVersion = python --version
Write-Host "Found: $pythonVersion" -ForegroundColor Green

# Step 2: Install UV package manager
Write-Host "`nStep 2: Installing UV package manager..." -ForegroundColor Yellow
try {
    pip install uv
    Write-Host "UV package manager installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to install UV package manager: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Install Python dependencies
Write-Host "`nStep 3: Installing Python dependencies..." -ForegroundColor Yellow
try {
    uv pip install --system -r pyproject.toml
    Write-Host "Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to install dependencies: $_" -ForegroundColor Red
    exit 1
}

# Step 4: Setup environment file
Write-Host "`nStep 4: Setting up environment file..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Copy-Item ".env.example" ".env"
    Write-Host "Environment file created. Please edit .env with your database credentials." -ForegroundColor Green
} else {
    Write-Host "Environment file already exists." -ForegroundColor Yellow
}

# Step 5: Database setup instructions
Write-Host "`nStep 5: Database setup instructions..." -ForegroundColor Yellow
Write-Host "Please ensure PostgreSQL is installed and running, then:" -ForegroundColor Cyan
Write-Host "1. Create database: CREATE DATABASE pim_db;" -ForegroundColor Cyan
Write-Host "2. Create user: CREATE USER postgres WITH PASSWORD 'password';" -ForegroundColor Cyan
Write-Host "3. Grant privileges: GRANT ALL PRIVILEGES ON DATABASE pim_db TO postgres;" -ForegroundColor Cyan
Write-Host "`nOr use Docker: docker-compose up -d postgres" -ForegroundColor Cyan

# Step 6: Run database migrations
Write-Host "`nStep 6: Running database migrations..." -ForegroundColor Yellow
try {
    alembic upgrade head
    Write-Host "Database migrations completed successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to run migrations. Please check your database connection." -ForegroundColor Red
    Write-Host "Make sure PostgreSQL is running and credentials in .env are correct." -ForegroundColor Red
    exit 1
}

# Step 7: Create sample data (optional)
Write-Host "`nStep 7: Creating sample data (optional)..." -ForegroundColor Yellow
$createSample = Read-Host "Do you want to create sample data? (y/n)"
if ($createSample -eq "y" -or $createSample -eq "Y") {
    try {
        python scripts/init_db.py
        Write-Host "Sample data created successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to create sample data: $_" -ForegroundColor Red
        exit 1
    }
}

# Success message
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Setup completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`nTo start the development server:" -ForegroundColor Cyan
Write-Host "  uvicorn app.main:app --reload --host 0.0.0.0 --port 8000" -ForegroundColor White

Write-Host "`nTo start with Docker:" -ForegroundColor Cyan
Write-Host "  docker-compose up -d" -ForegroundColor White

Write-Host "`nAPI Documentation will be available at:" -ForegroundColor Cyan
Write-Host "  http://localhost:8000/api/v1/docs" -ForegroundColor White

Write-Host "`nHealth check:" -ForegroundColor Cyan
Write-Host "  http://localhost:8000/health" -ForegroundColor White

Write-Host "`nPress any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
