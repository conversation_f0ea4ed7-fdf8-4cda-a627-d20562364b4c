"""
Product API endpoints.
"""
import logging
from typing import Any, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_database, get_common_params, CommonQueryParams
from app.crud.product import product as product_crud
from app.schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductResponse,
    ProductListResponse
)
from app.utils.response import APIResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=dict)
def get_products(
    common: CommonQueryParams = Depends(get_common_params),
    db: Session = Depends(get_database)
) -> Any:
    """
    Retrieve products with pagination, filtering, and sorting.
    
    - **page**: Page number (default: 1)
    - **size**: Page size (default: 10, max: 100)
    - **sort_by**: Field to sort by (default: created_at)
    - **sort_order**: Sort order - asc or desc (default: desc)
    - **search**: Search in product name
    - **category**: Filter by category
    - **brand**: Filter by brand
    - **is_active**: Filter by active status
    - **min_price**: Minimum price filter
    - **max_price**: Maximum price filter
    """
    try:
        products, total = product_crud.get_multi(
            db=db,
            skip=common.skip,
            limit=common.size,
            filters=common.filters,
            sort_by=common.sort_by,
            sort_order=common.sort_order
        )
        
        # Convert to response format
        product_responses = [ProductResponse.from_orm(product) for product in products]
        
        return APIResponse.paginated(
            items=product_responses,
            total=total,
            page=common.page,
            size=common.size,
            message="Products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error retrieving products: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve products"
        )


@router.get("/{product_id}", response_model=dict)
def get_product(
    product_id: UUID,
    db: Session = Depends(get_database)
) -> Any:
    """
    Get a specific product by ID.
    """
    try:
        product = product_crud.get(db=db, id=product_id)
        
        if not product:
            return APIResponse.not_found(
                message="Product not found",
                resource_id=str(product_id)
            )
        
        product_response = ProductResponse.from_orm(product)
        
        return APIResponse.success(
            data=product_response,
            message="Product retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error retrieving product {product_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve product"
        )


@router.post("/", response_model=dict, status_code=status.HTTP_201_CREATED)
def create_product(
    product_in: ProductCreate,
    db: Session = Depends(get_database)
) -> Any:
    """
    Create a new product.
    """
    try:
        # Check if product with same SKU already exists
        existing_product = product_crud.get_by_sku(db=db, sku=product_in.sku)
        if existing_product:
            return APIResponse.conflict(
                message="Product with this SKU already exists",
                errors={"sku": "SKU must be unique"}
            )
        
        # Create the product
        product = product_crud.create(db=db, obj_in=product_in)
        product_response = ProductResponse.from_orm(product)
        
        return APIResponse.created(
            data=product_response,
            message="Product created successfully"
        )
        
    except ValueError as e:
        logger.warning(f"Validation error creating product: {e}")
        return APIResponse.conflict(
            message=str(e),
            errors={"sku": "SKU must be unique"}
        )
    except Exception as e:
        logger.error(f"Error creating product: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create product"
        )


@router.patch("/{product_id}", response_model=dict)
def update_product(
    product_id: UUID,
    product_in: ProductUpdate,
    db: Session = Depends(get_database)
) -> Any:
    """
    Update an existing product (partial update).
    """
    try:
        # Get existing product
        product = product_crud.get(db=db, id=product_id)
        if not product:
            return APIResponse.not_found(
                message="Product not found",
                resource_id=str(product_id)
            )
        
        # Check if SKU is being updated and if it conflicts
        if product_in.sku and product_in.sku != product.sku:
            existing_product = product_crud.get_by_sku(db=db, sku=product_in.sku)
            if existing_product:
                return APIResponse.conflict(
                    message="Product with this SKU already exists",
                    errors={"sku": "SKU must be unique"}
                )
        
        # Update the product
        updated_product = product_crud.update(
            db=db, db_obj=product, obj_in=product_in
        )
        product_response = ProductResponse.from_orm(updated_product)
        
        return APIResponse.success(
            data=product_response,
            message="Product updated successfully"
        )
        
    except ValueError as e:
        logger.warning(f"Validation error updating product: {e}")
        return APIResponse.conflict(
            message=str(e),
            errors={"sku": "SKU must be unique"}
        )
    except Exception as e:
        logger.error(f"Error updating product {product_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update product"
        )


@router.delete("/{product_id}", response_model=dict)
def delete_product(
    product_id: UUID,
    db: Session = Depends(get_database)
) -> Any:
    """
    Delete a product by ID.
    """
    try:
        product = product_crud.delete(db=db, id=product_id)
        
        if not product:
            return APIResponse.not_found(
                message="Product not found",
                resource_id=str(product_id)
            )
        
        return APIResponse.success(
            message="Product deleted successfully"
        )
        
    except Exception as e:
        logger.error(f"Error deleting product {product_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete product"
        )
