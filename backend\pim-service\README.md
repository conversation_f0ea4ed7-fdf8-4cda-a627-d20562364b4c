# PIM Service - Product Information Management

A production-ready microservice for Product Information Management (PIM) built with FastAPI, PostgreSQL, SQLAlchemy, and Alembic.

## Features

- **RESTful API** with full CRUD operations for products
- **PostgreSQL** database with connection pooling
- **SQLAlchemy** ORM with Alembic migrations
- **FastAPI** with automatic API documentation
- **Pydantic** for data validation and serialization
- **Docker** containerization with docker-compose
- **Production-ready** error handling and logging
- **Standardized API responses** with consistent format
- **Pagination, filtering, and sorting** support
- **Health check** endpoint
- **UV package manager** for fast dependency management

## API Endpoints

### Products

- `GET /api/v1/products` - List products with pagination, filtering, and sorting
- `GET /api/v1/products/{id}` - Get a specific product by ID
- `POST /api/v1/products` - Create a new product
- `PATCH /api/v1/products/{id}` - Update an existing product (partial update)
- `DELETE /api/v1/products/{id}` - Delete a product by ID

### System

- `GET /health` - Health check endpoint
- `GET /` - Root endpoint with service information
- `GET /api/v1/docs` - Interactive API documentation (Swagger UI)
- `GET /api/v1/redoc` - Alternative API documentation (ReDoc)

## Database Schema

### Products Table (`pim_products`)

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key (auto-generated) |
| name | VARCHAR(255) | Product name (required) |
| sku | VARCHAR(100) | Stock Keeping Unit (unique, required) |
| description | TEXT | Product description (optional) |
| price | NUMERIC(10,2) | Product price (required) |
| inventory_level | INTEGER | Current inventory level (default: 0) |
| is_active | BOOLEAN | Product active status (default: true) |
| category | VARCHAR(100) | Product category (optional) |
| brand | VARCHAR(100) | Product brand (optional) |
| weight | NUMERIC(8,3) | Product weight in kg (optional) |
| dimensions | VARCHAR(100) | Product dimensions (optional) |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

## Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL 15+
- Docker and Docker Compose (optional)
- UV package manager

### Windows Setup (Automated)

For Windows users, we provide automated setup scripts:

#### Option A: PowerShell Script (Recommended)
```powershell
# Navigate to the project directory
cd backend\pim-service

# Run the PowerShell setup script
.\setup.ps1
```

#### Option B: Batch Script
```cmd
# Navigate to the project directory
cd backend\pim-service

# Run the batch setup script
setup.bat
```

### Option 1: Docker Compose (Recommended)

1. **Navigate to the project:**
   ```cmd
   cd backend\pim-service
   ```

2. **Copy environment file:**
   ```cmd
   copy .env.example .env
   ```

3. **Start services:**
   ```cmd
   docker-compose up -d
   ```

4. **Run database migrations:**
   ```cmd
   docker-compose exec pim-service alembic upgrade head
   ```

5. **Initialize with sample data (optional):**
   ```cmd
   docker-compose exec pim-service python scripts/init_db.py
   ```

6. **Access the API:**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/api/v1/docs
   - Health Check: http://localhost:8000/health

### Option 2: Manual Local Development (Windows)

1. **Install UV package manager:**
   ```cmd
   pip install uv
   ```

2. **Install dependencies:**
   ```cmd
   uv pip install --system -r pyproject.toml
   ```

3. **Set up PostgreSQL database:**

   Install PostgreSQL from https://www.postgresql.org/download/windows/

   Then create the database:
   ```sql
   CREATE DATABASE pim_db;
   CREATE USER postgres WITH PASSWORD 'password';
   GRANT ALL PRIVILEGES ON DATABASE pim_db TO postgres;
   ```

4. **Copy and configure environment:**
   ```cmd
   copy .env.example .env
   # Edit .env with your database credentials using notepad or your preferred editor
   notepad .env
   ```

5. **Run database migrations:**
   ```cmd
   alembic upgrade head
   ```

6. **Initialize with sample data (optional):**
   ```cmd
   python scripts\init_db.py
   ```

7. **Start the development server:**
   ```cmd
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/pim_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=pim_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Application Configuration
APP_NAME=PIM Service
APP_VERSION=0.1.0
DEBUG=True
ENVIRONMENT=development

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Product Information Management Service

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Logging Configuration
LOG_LEVEL=INFO
```

## Database Migrations

### Create a new migration:
```bash
alembic revision --autogenerate -m "Description of changes"
```

### Apply migrations:
```bash
alembic upgrade head
```

### Rollback migration:
```bash
alembic downgrade -1
```

## API Usage Examples

### Create a Product
```bash
curl -X POST "http://localhost:8000/api/v1/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Wireless Mouse",
    "sku": "WM-001",
    "description": "Ergonomic wireless mouse",
    "price": 29.99,
    "inventory_level": 100,
    "category": "Electronics",
    "brand": "TechGear"
  }'
```

### Get Products with Filtering
```bash
curl "http://localhost:8000/api/v1/products?category=Electronics&page=1&size=10&sort_by=name&sort_order=asc"
```

### Update a Product
```bash
curl -X PATCH "http://localhost:8000/api/v1/products/{product_id}" \
  -H "Content-Type: application/json" \
  -d '{
    "price": 24.99,
    "inventory_level": 150
  }'
```

## Testing

### Run tests:
```bash
pytest
```

### Run tests with coverage:
```bash
pytest --cov=app tests/
```

## Production Deployment

1. **Set production environment variables**
2. **Use a production WSGI server** (already configured with uvicorn)
3. **Set up proper database credentials and connection pooling**
4. **Configure logging and monitoring**
5. **Set up SSL/TLS termination**
6. **Use a reverse proxy** (nginx, traefik, etc.)

## Project Structure

```
pim-service/
├── app/
│   ├── api/
│   │   ├── deps.py              # API dependencies
│   │   └── v1/
│   │       ├── api.py           # API router
│   │       └── endpoints/
│   │           └── products.py  # Product endpoints
│   ├── core/
│   │   ├── config.py           # Configuration
│   │   └── database.py         # Database connection
│   ├── crud/
│   │   └── product.py          # CRUD operations
│   ├── models/
│   │   └── product.py          # SQLAlchemy models
│   ├── schemas/
│   │   └── product.py          # Pydantic schemas
│   ├── utils/
│   │   └── response.py         # Response utilities
│   └── main.py                 # FastAPI application
├── alembic/                    # Database migrations
├── scripts/                    # Utility scripts
├── tests/                      # Test files
├── docker-compose.yml          # Docker services
├── Dockerfile                  # Container definition
├── pyproject.toml             # Dependencies and config
└── README.md                  # This file
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is licensed under the MIT License.
