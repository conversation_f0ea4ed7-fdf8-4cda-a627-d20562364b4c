# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/pim_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=pim_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Application Configuration
APP_NAME=PIM Service
APP_VERSION=0.1.0
DEBUG=True
ENVIRONMENT=development

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Product Information Management Service

# Security Configuration (Optional for future use)
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Logging Configuration
LOG_LEVEL=INFO
