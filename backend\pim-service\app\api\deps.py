"""
API dependencies.
"""
from typing import Generator, Optional
from fastapi import Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db


def get_database() -> Generator[Session, None, None]:
    """Get database session dependency."""
    return get_db()


class CommonQueryParams:
    """Common query parameters for pagination and filtering."""
    
    def __init__(
        self,
        page: int = Query(1, ge=1, description="Page number"),
        size: int = Query(10, ge=1, le=100, description="Page size"),
        sort_by: str = Query("created_at", description="Field to sort by"),
        sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
        search: Optional[str] = Query(None, description="Search term"),
        category: Optional[str] = Query(None, description="Filter by category"),
        brand: Optional[str] = Query(None, description="Filter by brand"),
        is_active: Optional[bool] = Query(None, description="Filter by active status"),
        min_price: Optional[float] = Query(None, ge=0, description="Minimum price filter"),
        max_price: Optional[float] = Query(None, ge=0, description="Maximum price filter"),
    ):
        self.page = page
        self.size = size
        self.sort_by = sort_by
        self.sort_order = sort_order
        self.search = search
        self.category = category
        self.brand = brand
        self.is_active = is_active
        self.min_price = min_price
        self.max_price = max_price
        
        # Validate max_price is greater than min_price
        if (
            self.min_price is not None 
            and self.max_price is not None 
            and self.max_price < self.min_price
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="max_price must be greater than or equal to min_price"
            )
    
    @property
    def skip(self) -> int:
        """Calculate skip value for pagination."""
        return (self.page - 1) * self.size
    
    @property
    def filters(self) -> dict:
        """Get filters as dictionary."""
        filters = {}
        
        if self.search:
            filters["name"] = self.search
        if self.category:
            filters["category"] = self.category
        if self.brand:
            filters["brand"] = self.brand
        if self.is_active is not None:
            filters["is_active"] = self.is_active
        if self.min_price is not None:
            filters["min_price"] = self.min_price
        if self.max_price is not None:
            filters["max_price"] = self.max_price
            
        return filters


def get_common_params(
    common: CommonQueryParams = Depends()
) -> CommonQueryParams:
    """Dependency to get common query parameters."""
    return common
