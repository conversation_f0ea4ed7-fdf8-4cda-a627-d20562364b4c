"""
CRUD operations for products.
"""
import logging
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import IntegrityError

from app.models.product import Product
from app.schemas.product import ProductCreate, ProductUpdate

logger = logging.getLogger(__name__)


class ProductCRUD:
    """CRUD operations for Product model."""
    
    def create(self, db: Session, *, obj_in: ProductCreate) -> Product:
        """
        Create a new product.
        
        Args:
            db: Database session
            obj_in: Product creation data
            
        Returns:
            Product: Created product
            
        Raises:
            IntegrityError: If SKU already exists
        """
        try:
            db_obj = Product(**obj_in.dict())
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            logger.info(f"Created product with ID: {db_obj.id}")
            return db_obj
        except IntegrityError as e:
            db.rollback()
            logger.error(f"Failed to create product: {e}")
            raise ValueError("Product with this SKU already exists")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error creating product: {e}")
            raise
    
    def get(self, db: Session, id: UUID) -> Optional[Product]:
        """
        Get product by ID.
        
        Args:
            db: Database session
            id: Product ID
            
        Returns:
            Product or None: Product if found, None otherwise
        """
        try:
            return db.query(Product).filter(Product.id == id).first()
        except Exception as e:
            logger.error(f"Error getting product {id}: {e}")
            raise
    
    def get_by_sku(self, db: Session, sku: str) -> Optional[Product]:
        """
        Get product by SKU.
        
        Args:
            db: Database session
            sku: Product SKU
            
        Returns:
            Product or None: Product if found, None otherwise
        """
        try:
            return db.query(Product).filter(Product.sku == sku.upper()).first()
        except Exception as e:
            logger.error(f"Error getting product by SKU {sku}: {e}")
            raise
    
    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> tuple[List[Product], int]:
        """
        Get multiple products with pagination, filtering, and sorting.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Dictionary of filters to apply
            sort_by: Field to sort by
            sort_order: Sort order ('asc' or 'desc')
            
        Returns:
            tuple: (List of products, total count)
        """
        try:
            query = db.query(Product)
            
            # Apply filters
            if filters:
                filter_conditions = []
                
                if filters.get("name"):
                    filter_conditions.append(
                        Product.name.ilike(f"%{filters['name']}%")
                    )
                
                if filters.get("sku"):
                    filter_conditions.append(
                        Product.sku.ilike(f"%{filters['sku']}%")
                    )
                
                if filters.get("category"):
                    filter_conditions.append(
                        Product.category.ilike(f"%{filters['category']}%")
                    )
                
                if filters.get("brand"):
                    filter_conditions.append(
                        Product.brand.ilike(f"%{filters['brand']}%")
                    )
                
                if filters.get("is_active") is not None:
                    filter_conditions.append(
                        Product.is_active == filters["is_active"]
                    )
                
                if filters.get("min_price") is not None:
                    filter_conditions.append(
                        Product.price >= filters["min_price"]
                    )
                
                if filters.get("max_price") is not None:
                    filter_conditions.append(
                        Product.price <= filters["max_price"]
                    )
                
                if filter_conditions:
                    query = query.filter(and_(*filter_conditions))
            
            # Get total count before pagination
            total = query.count()
            
            # Apply sorting
            if hasattr(Product, sort_by):
                sort_column = getattr(Product, sort_by)
                if sort_order.lower() == "desc":
                    query = query.order_by(desc(sort_column))
                else:
                    query = query.order_by(asc(sort_column))
            
            # Apply pagination
            products = query.offset(skip).limit(limit).all()
            
            return products, total
            
        except Exception as e:
            logger.error(f"Error getting products: {e}")
            raise
    
    def update(
        self, db: Session, *, db_obj: Product, obj_in: ProductUpdate
    ) -> Product:
        """
        Update a product.
        
        Args:
            db: Database session
            db_obj: Existing product object
            obj_in: Update data
            
        Returns:
            Product: Updated product
        """
        try:
            update_data = obj_in.dict(exclude_unset=True)
            
            for field, value in update_data.items():
                setattr(db_obj, field, value)
            
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            logger.info(f"Updated product with ID: {db_obj.id}")
            return db_obj
            
        except IntegrityError as e:
            db.rollback()
            logger.error(f"Failed to update product: {e}")
            raise ValueError("Product with this SKU already exists")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error updating product: {e}")
            raise
    
    def delete(self, db: Session, *, id: UUID) -> Optional[Product]:
        """
        Delete a product.
        
        Args:
            db: Database session
            id: Product ID
            
        Returns:
            Product or None: Deleted product if found, None otherwise
        """
        try:
            obj = db.query(Product).get(id)
            if obj:
                db.delete(obj)
                db.commit()
                logger.info(f"Deleted product with ID: {id}")
            return obj
        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting product {id}: {e}")
            raise


# Create instance
product = ProductCRUD()
