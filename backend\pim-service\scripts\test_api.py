"""
Simple API test script to verify the PIM service is working correctly.
"""
import requests
import json
import sys
from decimal import Decimal

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_health_check():
    """Test the health check endpoint."""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Health check passed")
                return True
            else:
                print("❌ Health check failed - service unhealthy")
                return False
        else:
            print(f"❌ Health check failed - status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Health check failed - cannot connect to service")
        return False
    except Exception as e:
        print(f"❌ Health check failed - error: {e}")
        return False

def test_create_product():
    """Test creating a new product."""
    print("\nTesting product creation...")
    
    test_product = {
        "name": "Test API Product",
        "sku": "TEST-API-001",
        "description": "A test product created via API test script",
        "price": 19.99,
        "inventory_level": 50,
        "category": "Test Category",
        "brand": "Test Brand",
        "weight": 0.5,
        "dimensions": "10cm x 5cm x 2cm"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/products",
            json=test_product,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            data = response.json()
            if data.get("success"):
                product_id = data["data"]["id"]
                print(f"✅ Product created successfully - ID: {product_id}")
                return product_id
            else:
                print("❌ Product creation failed - response not successful")
                return None
        else:
            print(f"❌ Product creation failed - status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Product creation failed - error: {e}")
        return None

def test_get_products():
    """Test getting list of products."""
    print("\nTesting product list retrieval...")
    
    try:
        response = requests.get(f"{API_BASE}/products")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                items = data["data"]["items"]
                pagination = data["data"]["pagination"]
                print(f"✅ Products retrieved successfully - {len(items)} items, total: {pagination['total']}")
                return True
            else:
                print("❌ Product list retrieval failed - response not successful")
                return False
        else:
            print(f"❌ Product list retrieval failed - status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Product list retrieval failed - error: {e}")
        return False

def test_get_product(product_id):
    """Test getting a specific product."""
    print(f"\nTesting product retrieval for ID: {product_id}...")
    
    try:
        response = requests.get(f"{API_BASE}/products/{product_id}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                product = data["data"]
                print(f"✅ Product retrieved successfully - Name: {product['name']}")
                return True
            else:
                print("❌ Product retrieval failed - response not successful")
                return False
        else:
            print(f"❌ Product retrieval failed - status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Product retrieval failed - error: {e}")
        return False

def test_update_product(product_id):
    """Test updating a product."""
    print(f"\nTesting product update for ID: {product_id}...")
    
    update_data = {
        "price": 24.99,
        "inventory_level": 75,
        "description": "Updated test product description"
    }
    
    try:
        response = requests.patch(
            f"{API_BASE}/products/{product_id}",
            json=update_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                product = data["data"]
                print(f"✅ Product updated successfully - New price: {product['price']}")
                return True
            else:
                print("❌ Product update failed - response not successful")
                return False
        else:
            print(f"❌ Product update failed - status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Product update failed - error: {e}")
        return False

def test_delete_product(product_id):
    """Test deleting a product."""
    print(f"\nTesting product deletion for ID: {product_id}...")
    
    try:
        response = requests.delete(f"{API_BASE}/products/{product_id}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("✅ Product deleted successfully")
                return True
            else:
                print("❌ Product deletion failed - response not successful")
                return False
        else:
            print(f"❌ Product deletion failed - status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Product deletion failed - error: {e}")
        return False

def main():
    """Run all API tests."""
    print("=" * 50)
    print("PIM Service API Test Script")
    print("=" * 50)
    
    # Test health check first
    if not test_health_check():
        print("\n❌ Service is not healthy. Please check if the service is running.")
        sys.exit(1)
    
    # Test CRUD operations
    product_id = test_create_product()
    if not product_id:
        print("\n❌ Cannot continue tests without creating a product.")
        sys.exit(1)
    
    success_count = 1  # Health check and create product passed
    total_tests = 6
    
    if test_get_products():
        success_count += 1
    
    if test_get_product(product_id):
        success_count += 1
    
    if test_update_product(product_id):
        success_count += 1
    
    if test_delete_product(product_id):
        success_count += 1
    
    # Final verification - try to get deleted product (should fail)
    print(f"\nTesting deleted product retrieval (should fail)...")
    response = requests.get(f"{API_BASE}/products/{product_id}")
    if response.status_code == 404:
        print("✅ Deleted product correctly returns 404")
        success_count += 1
    else:
        print("❌ Deleted product should return 404")
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Tests passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 All tests passed! PIM Service is working correctly.")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Please check the service configuration.")
        sys.exit(1)

if __name__ == "__main__":
    main()
