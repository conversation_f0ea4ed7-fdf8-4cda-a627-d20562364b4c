@echo off
echo ========================================
echo PIM Service Setup Script for Windows
echo ========================================

echo.
echo Step 1: Installing UV package manager...
pip install uv
if %errorlevel% neq 0 (
    echo Failed to install UV package manager
    pause
    exit /b 1
)

echo.
echo Step 2: Installing Python dependencies...
uv pip install --system -r pyproject.toml
if %errorlevel% neq 0 (
    echo Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Step 3: Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo Environment file created. Please edit .env with your database credentials.
) else (
    echo Environment file already exists.
)

echo.
echo Step 4: Database setup instructions...
echo Please ensure PostgreSQL is installed and running, then:
echo 1. Create database: CREATE DATABASE pim_db;
echo 2. Create user: CREATE USER postgres WITH PASSWORD 'password';
echo 3. Grant privileges: GRANT ALL PRIVILEGES ON DATABASE pim_db TO postgres;
echo.
echo Or use Docker: docker-compose up -d postgres

echo.
echo Step 5: Running database migrations...
alembic upgrade head
if %errorlevel% neq 0 (
    echo Failed to run migrations. Please check your database connection.
    echo Make sure PostgreSQL is running and credentials in .env are correct.
    pause
    exit /b 1
)

echo.
echo Step 6: Creating sample data (optional)...
set /p create_sample="Do you want to create sample data? (y/n): "
if /i "%create_sample%"=="y" (
    python scripts/init_db.py
    if %errorlevel% neq 0 (
        echo Failed to create sample data
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To start the development server:
echo   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
echo.
echo To start with Docker:
echo   docker-compose up -d
echo.
echo API Documentation will be available at:
echo   http://localhost:8000/api/v1/docs
echo.
echo Health check:
echo   http://localhost:8000/health
echo.
pause
