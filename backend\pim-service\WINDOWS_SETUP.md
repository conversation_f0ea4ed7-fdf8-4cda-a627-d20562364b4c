# Windows Setup Guide for PIM Service

This guide provides step-by-step instructions for setting up the PIM (Product Information Management) service on Windows.

## Prerequisites

Before starting, ensure you have the following installed:

### Required Software

1. **Python 3.11 or higher**
   - Download from: https://www.python.org/downloads/windows/
   - During installation, make sure to check "Add Python to PATH"
   - Verify installation: `python --version`

2. **PostgreSQL 15 or higher**
   - Download from: https://www.postgresql.org/download/windows/
   - During installation, remember the password for the `postgres` user
   - Default port: 5432

3. **Git** (if cloning from repository)
   - Download from: https://git-scm.com/download/win

### Optional Software

4. **Docker Desktop** (for containerized setup)
   - Download from: https://www.docker.com/products/docker-desktop/
   - Requires Windows 10/11 Pro, Enterprise, or Education

5. **Visual Studio Code** (recommended IDE)
   - Download from: https://code.visualstudio.com/

## Setup Methods

Choose one of the following setup methods:

### Method 1: Automated Setup (Recommended)

We provide automated setup scripts for Windows:

#### Using PowerShell (Recommended)

1. Open PowerShell as Administrator
2. Navigate to the project directory:
   ```powershell
   cd C:\Project\Full-stack-web-app\backend\pim-service
   ```
3. Run the setup script:
   ```powershell
   .\setup.ps1
   ```

#### Using Command Prompt

1. Open Command Prompt as Administrator
2. Navigate to the project directory:
   ```cmd
   cd C:\Project\Full-stack-web-app\backend\pim-service
   ```
3. Run the setup script:
   ```cmd
   setup.bat
   ```

### Method 2: Docker Setup (Easiest)

If you have Docker Desktop installed:

1. Open PowerShell or Command Prompt
2. Navigate to the project directory:
   ```cmd
   cd C:\Project\Full-stack-web-app\backend\pim-service
   ```
3. Copy environment file:
   ```cmd
   copy .env.example .env
   ```
4. Start all services:
   ```cmd
   docker-compose up -d
   ```
5. Wait for services to start, then run migrations:
   ```cmd
   docker-compose exec pim-service alembic upgrade head
   ```
6. (Optional) Create sample data:
   ```cmd
   docker-compose exec pim-service python scripts/init_db.py
   ```

### Method 3: Manual Setup

#### Step 1: Install UV Package Manager

```cmd
pip install uv
```

#### Step 2: Install Python Dependencies

```cmd
uv pip install --system -r pyproject.toml
```

#### Step 3: Setup PostgreSQL Database

1. Open pgAdmin or connect via command line:
   ```cmd
   psql -U postgres -h localhost
   ```

2. Create the database and user:
   ```sql
   CREATE DATABASE pim_db;
   CREATE USER pim_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE pim_db TO pim_user;
   \q
   ```

#### Step 4: Configure Environment

1. Copy the environment template:
   ```cmd
   copy .env.example .env
   ```

2. Edit the `.env` file with your database credentials:
   ```cmd
   notepad .env
   ```

   Update these values:
   ```env
   POSTGRES_USER=pim_user
   POSTGRES_PASSWORD=your_password
   POSTGRES_DB=pim_db
   POSTGRES_HOST=localhost
   POSTGRES_PORT=5432
   ```

#### Step 5: Run Database Migrations

```cmd
alembic upgrade head
```

#### Step 6: Create Sample Data (Optional)

```cmd
python scripts\init_db.py
```

#### Step 7: Start the Development Server

```cmd
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## Verification

After setup, verify the installation:

1. **Health Check**: http://localhost:8000/health
2. **API Documentation**: http://localhost:8000/api/v1/docs
3. **Alternative Docs**: http://localhost:8000/api/v1/redoc

## Common Issues and Solutions

### Issue 1: Python not found
**Error**: `'python' is not recognized as an internal or external command`

**Solution**: 
- Reinstall Python and check "Add Python to PATH"
- Or add Python to PATH manually:
  1. Search for "Environment Variables" in Windows
  2. Add Python installation directory to PATH

### Issue 2: PostgreSQL connection failed
**Error**: `could not connect to server`

**Solutions**:
- Ensure PostgreSQL service is running:
  ```cmd
  net start postgresql-x64-15
  ```
- Check if port 5432 is available:
  ```cmd
  netstat -an | findstr :5432
  ```
- Verify credentials in `.env` file

### Issue 3: Port 8000 already in use
**Error**: `[Errno 10048] Only one usage of each socket address`

**Solutions**:
- Find and kill the process using port 8000:
  ```cmd
  netstat -ano | findstr :8000
  taskkill /PID <PID_NUMBER> /F
  ```
- Or use a different port:
  ```cmd
  uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
  ```

### Issue 4: UV installation fails
**Error**: Various pip/uv installation errors

**Solutions**:
- Upgrade pip first:
  ```cmd
  python -m pip install --upgrade pip
  ```
- Use virtual environment:
  ```cmd
  python -m venv venv
  venv\Scripts\activate
  pip install uv
  ```

### Issue 5: Alembic migration fails
**Error**: `Target database is not up to date`

**Solutions**:
- Check database connection
- Ensure database exists and user has permissions
- Reset migrations if needed:
  ```cmd
  alembic stamp head
  ```

## Development Workflow

### Starting the Service

```cmd
# Method 1: Direct Python
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Method 2: Docker
docker-compose up -d

# Method 3: With specific environment
set ENVIRONMENT=development
uvicorn app.main:app --reload
```

### Running Tests

```cmd
# Install test dependencies
uv pip install --system pytest pytest-asyncio httpx

# Run tests
pytest

# Run with coverage
pytest --cov=app tests/
```

### Database Operations

```cmd
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1

# Check current revision
alembic current
```

### Stopping Services

```cmd
# Stop Docker services
docker-compose down

# Stop development server
# Press Ctrl+C in the terminal where uvicorn is running
```

## Production Deployment on Windows

For production deployment on Windows Server:

1. **Use Windows Service**:
   - Install `python-windows-service`
   - Create service wrapper for uvicorn

2. **Use IIS with FastCGI**:
   - Install IIS with FastCGI module
   - Configure FastCGI for Python

3. **Use Docker in Production**:
   - Use Docker Swarm or Kubernetes
   - Configure proper logging and monitoring

## Support

If you encounter issues not covered in this guide:

1. Check the main README.md for general troubleshooting
2. Review the application logs
3. Ensure all prerequisites are properly installed
4. Verify network connectivity and firewall settings

## Next Steps

After successful setup:

1. Explore the API documentation at http://localhost:8000/api/v1/docs
2. Test the API endpoints using the interactive documentation
3. Review the code structure in the project directory
4. Consider setting up a development environment with your preferred IDE
