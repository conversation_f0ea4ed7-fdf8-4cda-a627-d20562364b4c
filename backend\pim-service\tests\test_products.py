"""
Tests for product API endpoints.
"""
import pytest
from fastapi.testclient import TestClient


def test_health_check(client: TestClient):
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "service" in data["data"]


def test_root_endpoint(client: TestClient):
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "service" in data["data"]


def test_create_product(client: TestClient, sample_product_data):
    """Test creating a new product."""
    response = client.post("/api/v1/products", json=sample_product_data)
    assert response.status_code == 201
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == sample_product_data["name"]
    assert data["data"]["sku"] == sample_product_data["sku"]


def test_create_product_duplicate_sku(client: TestClient, sample_product_data):
    """Test creating a product with duplicate SKU."""
    # Create first product
    client.post("/api/v1/products", json=sample_product_data)
    
    # Try to create another product with same SKU
    response = client.post("/api/v1/products", json=sample_product_data)
    assert response.status_code == 409
    data = response.json()
    assert data["success"] is False


def test_get_products(client: TestClient, sample_product_data):
    """Test getting list of products."""
    # Create a product first
    client.post("/api/v1/products", json=sample_product_data)
    
    # Get products
    response = client.get("/api/v1/products")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "items" in data["data"]
    assert "pagination" in data["data"]


def test_get_product_by_id(client: TestClient, sample_product_data):
    """Test getting a specific product by ID."""
    # Create a product first
    create_response = client.post("/api/v1/products", json=sample_product_data)
    product_id = create_response.json()["data"]["id"]
    
    # Get the product
    response = client.get(f"/api/v1/products/{product_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == product_id


def test_get_nonexistent_product(client: TestClient):
    """Test getting a non-existent product."""
    fake_id = "00000000-0000-0000-0000-000000000000"
    response = client.get(f"/api/v1/products/{fake_id}")
    assert response.status_code == 404
    data = response.json()
    assert data["success"] is False


def test_update_product(client: TestClient, sample_product_data):
    """Test updating a product."""
    # Create a product first
    create_response = client.post("/api/v1/products", json=sample_product_data)
    product_id = create_response.json()["data"]["id"]
    
    # Update the product
    update_data = {"price": 29.99, "inventory_level": 20}
    response = client.patch(f"/api/v1/products/{product_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["price"] == 29.99
    assert data["data"]["inventory_level"] == 20


def test_delete_product(client: TestClient, sample_product_data):
    """Test deleting a product."""
    # Create a product first
    create_response = client.post("/api/v1/products", json=sample_product_data)
    product_id = create_response.json()["data"]["id"]
    
    # Delete the product
    response = client.delete(f"/api/v1/products/{product_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    
    # Verify product is deleted
    get_response = client.get(f"/api/v1/products/{product_id}")
    assert get_response.status_code == 404


def test_product_validation(client: TestClient):
    """Test product validation."""
    invalid_data = {
        "name": "",  # Empty name should fail
        "sku": "TEST-001",
        "price": -10  # Negative price should fail
    }
    
    response = client.post("/api/v1/products", json=invalid_data)
    assert response.status_code == 422
    data = response.json()
    assert data["success"] is False


def test_products_pagination(client: TestClient):
    """Test products pagination."""
    # Create multiple products
    for i in range(15):
        product_data = {
            "name": f"Product {i}",
            "sku": f"PROD-{i:03d}",
            "price": 10.0 + i,
            "inventory_level": 10
        }
        client.post("/api/v1/products", json=product_data)
    
    # Test pagination
    response = client.get("/api/v1/products?page=1&size=10")
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]["items"]) == 10
    assert data["data"]["pagination"]["total"] == 15
    assert data["data"]["pagination"]["pages"] == 2


def test_products_filtering(client: TestClient, sample_product_data):
    """Test products filtering."""
    # Create a product
    client.post("/api/v1/products", json=sample_product_data)
    
    # Test category filter
    response = client.get(f"/api/v1/products?category={sample_product_data['category']}")
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]["items"]) >= 1
    
    # Test search filter
    response = client.get(f"/api/v1/products?search={sample_product_data['name']}")
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]["items"]) >= 1
