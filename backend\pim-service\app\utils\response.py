"""
Standardized API response utilities.
"""
from typing import Any, Dict, Optional, Union
from fastapi import status
from fastapi.responses import JSONResponse


class APIResponse:
    """Standardized API response format."""
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "Success",
        status_code: int = status.HTTP_200_OK,
        meta: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a successful response.
        
        Args:
            data: Response data
            message: Success message
            status_code: HTTP status code
            meta: Additional metadata
            
        Returns:
            Dict: Standardized response format
        """
        response = {
            "success": True,
            "status_code": status_code,
            "message": message,
            "data": data
        }
        
        if meta:
            response["meta"] = meta
            
        return response
    
    @staticmethod
    def error(
        message: str = "An error occurred",
        status_code: int = status.HTTP_400_BAD_REQUEST,
        errors: Optional[Union[str, list, dict]] = None,
        data: Any = None
    ) -> Dict[str, Any]:
        """
        Create an error response.
        
        Args:
            message: Error message
            status_code: HTTP status code
            errors: Detailed error information
            data: Additional data (optional)
            
        Returns:
            Dict: Standardized error response format
        """
        response = {
            "success": False,
            "status_code": status_code,
            "message": message,
            "data": data
        }
        
        if errors:
            response["errors"] = errors
            
        return response
    
    @staticmethod
    def paginated(
        items: list,
        total: int,
        page: int,
        size: int,
        message: str = "Success"
    ) -> Dict[str, Any]:
        """
        Create a paginated response.
        
        Args:
            items: List of items
            total: Total number of items
            page: Current page number
            size: Page size
            message: Success message
            
        Returns:
            Dict: Standardized paginated response
        """
        pages = (total + size - 1) // size  # Calculate total pages
        
        return APIResponse.success(
            data={
                "items": items,
                "pagination": {
                    "total": total,
                    "page": page,
                    "size": size,
                    "pages": pages,
                    "has_next": page < pages,
                    "has_prev": page > 1
                }
            },
            message=message
        )
    
    @staticmethod
    def created(
        data: Any = None,
        message: str = "Resource created successfully"
    ) -> Dict[str, Any]:
        """Create a 201 Created response."""
        return APIResponse.success(
            data=data,
            message=message,
            status_code=status.HTTP_201_CREATED
        )
    
    @staticmethod
    def not_found(
        message: str = "Resource not found",
        resource_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a 404 Not Found response."""
        if resource_id:
            message = f"{message}: {resource_id}"
        
        return APIResponse.error(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND
        )
    
    @staticmethod
    def bad_request(
        message: str = "Bad request",
        errors: Optional[Union[str, list, dict]] = None
    ) -> Dict[str, Any]:
        """Create a 400 Bad Request response."""
        return APIResponse.error(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            errors=errors
        )
    
    @staticmethod
    def conflict(
        message: str = "Resource already exists",
        errors: Optional[Union[str, list, dict]] = None
    ) -> Dict[str, Any]:
        """Create a 409 Conflict response."""
        return APIResponse.error(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            errors=errors
        )
    
    @staticmethod
    def internal_error(
        message: str = "Internal server error"
    ) -> Dict[str, Any]:
        """Create a 500 Internal Server Error response."""
        return APIResponse.error(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def create_response(
    success: bool,
    data: Any = None,
    message: str = "",
    status_code: int = status.HTTP_200_OK,
    **kwargs
) -> JSONResponse:
    """
    Create a JSONResponse with standardized format.
    
    Args:
        success: Whether the request was successful
        data: Response data
        message: Response message
        status_code: HTTP status code
        **kwargs: Additional response fields
        
    Returns:
        JSONResponse: FastAPI JSON response
    """
    if success:
        response_data = APIResponse.success(
            data=data,
            message=message or "Success",
            status_code=status_code,
            **kwargs
        )
    else:
        response_data = APIResponse.error(
            message=message or "An error occurred",
            status_code=status_code,
            data=data,
            **kwargs
        )
    
    return JSONResponse(
        content=response_data,
        status_code=status_code
    )
