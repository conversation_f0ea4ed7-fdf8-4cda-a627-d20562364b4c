"""
SQLAlchemy models for products.
"""
import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, String, Text, Integer, Numeric, DateTime, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from app.core.database import Base


class Product(Base):
    """Product model for storing product information."""
    
    __tablename__ = "pim_products"
    
    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True,
        comment="Unique identifier for the product"
    )
    
    # Basic product information
    name = Column(
        String(255),
        nullable=False,
        index=True,
        comment="Product name"
    )
    
    sku = Column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        comment="Stock Keeping Unit - unique product identifier"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="Detailed product description"
    )
    
    # Pricing and inventory
    price = Column(
        Numeric(10, 2),
        nullable=False,
        comment="Product price with 2 decimal places"
    )
    
    inventory_level = Column(
        Integer,
        nullable=False,
        default=0,
        comment="Current inventory/stock level"
    )
    
    # Product status
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        comment="Whether the product is active/available"
    )
    
    # Additional product attributes (can be extended)
    category = Column(
        String(100),
        nullable=True,
        index=True,
        comment="Product category"
    )
    
    brand = Column(
        String(100),
        nullable=True,
        index=True,
        comment="Product brand"
    )
    
    weight = Column(
        Numeric(8, 3),
        nullable=True,
        comment="Product weight in kg"
    )
    
    dimensions = Column(
        String(100),
        nullable=True,
        comment="Product dimensions (L x W x H)"
    )
    
    # Timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Timestamp when the product was created"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Timestamp when the product was last updated"
    )
    
    def __repr__(self) -> str:
        """String representation of the product."""
        return f"<Product(id={self.id}, name='{self.name}', sku='{self.sku}')>"
    
    def to_dict(self) -> dict:
        """Convert product to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "sku": self.sku,
            "description": self.description,
            "price": float(self.price) if self.price else None,
            "inventory_level": self.inventory_level,
            "is_active": self.is_active,
            "category": self.category,
            "brand": self.brand,
            "weight": float(self.weight) if self.weight else None,
            "dimensions": self.dimensions,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
