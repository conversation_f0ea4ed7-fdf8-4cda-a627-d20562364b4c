-- Database initialization script for PIM service
-- This script will be executed when the PostgreSQL container starts

-- Create the database if it doesn't exist (handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS pim_db;

-- Connect to the pim_db database
\c pim_db;

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Insert some sample data (optional - for testing)
-- This will be executed after Alembic creates the tables
-- You can remove this section if you don't want sample data

-- Note: The actual table creation will be handled by Alembic migrations
-- This script is mainly for database setup and extensions
