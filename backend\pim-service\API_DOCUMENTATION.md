# PIM Service API Documentation

## Overview

The PIM (Product Information Management) Service provides a RESTful API for managing product information. This document provides detailed information about all available endpoints, request/response formats, and usage examples.

## Base URL

- **Development**: `http://localhost:8000`
- **API Base Path**: `/api/v1`

## Authentication

Currently, the API does not require authentication. This can be added in future versions.

## Response Format

All API responses follow a standardized format:

### Success Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Success message",
  "data": {
    // Response data
  },
  "meta": {
    // Optional metadata (for pagination, etc.)
  }
}
```

### Error Response
```json
{
  "success": false,
  "status_code": 400,
  "message": "Error message",
  "errors": {
    // Detailed error information
  }
}
```

## Endpoints

### Health Check

#### GET /health
Check the health status of the service.

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Health check completed",
  "data": {
    "service": "PIM Service",
    "version": "0.1.0",
    "status": "healthy",
    "database": "connected"
  }
}
```

### Products

#### GET /api/v1/products
Retrieve a list of products with pagination, filtering, and sorting.

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `size` (integer, default: 10, max: 100): Number of items per page
- `sort_by` (string, default: "created_at"): Field to sort by
- `sort_order` (string, default: "desc"): Sort order ("asc" or "desc")
- `search` (string): Search in product name
- `category` (string): Filter by category
- `brand` (string): Filter by brand
- `is_active` (boolean): Filter by active status
- `min_price` (number): Minimum price filter
- `max_price` (number): Maximum price filter

**Example Request:**
```
GET /api/v1/products?page=1&size=10&category=Electronics&sort_by=name&sort_order=asc
```

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Products retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-**********00",
        "name": "Wireless Headphones",
        "sku": "WH-001",
        "description": "High-quality wireless headphones",
        "price": 99.99,
        "inventory_level": 50,
        "is_active": true,
        "category": "Electronics",
        "brand": "TechSound",
        "weight": 0.25,
        "dimensions": "20cm x 18cm x 8cm",
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "size": 10,
      "pages": 10,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

#### GET /api/v1/products/{id}
Retrieve a specific product by ID.

**Path Parameters:**
- `id` (UUID): Product ID

**Example Request:**
```
GET /api/v1/products/123e4567-e89b-12d3-a456-**********00
```

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Product retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-**********00",
    "name": "Wireless Headphones",
    "sku": "WH-001",
    "description": "High-quality wireless headphones",
    "price": 99.99,
    "inventory_level": 50,
    "is_active": true,
    "category": "Electronics",
    "brand": "TechSound",
    "weight": 0.25,
    "dimensions": "20cm x 18cm x 8cm",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

#### POST /api/v1/products
Create a new product.

**Request Body:**
```json
{
  "name": "Wireless Headphones",
  "sku": "WH-001",
  "description": "High-quality wireless headphones",
  "price": 99.99,
  "inventory_level": 50,
  "is_active": true,
  "category": "Electronics",
  "brand": "TechSound",
  "weight": 0.25,
  "dimensions": "20cm x 18cm x 8cm"
}
```

**Required Fields:**
- `name` (string, 1-255 characters)
- `sku` (string, 1-100 characters, unique)
- `price` (number, > 0, max 2 decimal places)

**Optional Fields:**
- `description` (string)
- `inventory_level` (integer, >= 0, default: 0)
- `is_active` (boolean, default: true)
- `category` (string, max 100 characters)
- `brand` (string, max 100 characters)
- `weight` (number, > 0, max 3 decimal places)
- `dimensions` (string, max 100 characters)

**Response (201 Created):**
```json
{
  "success": true,
  "status_code": 201,
  "message": "Product created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-**********00",
    "name": "Wireless Headphones",
    "sku": "WH-001",
    // ... other fields
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

#### PATCH /api/v1/products/{id}
Update an existing product (partial update).

**Path Parameters:**
- `id` (UUID): Product ID

**Request Body (all fields optional):**
```json
{
  "name": "Updated Product Name",
  "price": 89.99,
  "inventory_level": 75
}
```

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Product updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-**********00",
    "name": "Updated Product Name",
    "price": 89.99,
    "inventory_level": 75,
    // ... other fields
    "updated_at": "2024-01-01T13:00:00Z"
  }
}
```

#### DELETE /api/v1/products/{id}
Delete a product by ID.

**Path Parameters:**
- `id` (UUID): Product ID

**Response:**
```json
{
  "success": true,
  "status_code": 200,
  "message": "Product deleted successfully"
}
```

## Error Codes

### HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists (e.g., duplicate SKU)
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error

### Common Error Responses

#### Validation Error (422)
```json
{
  "success": false,
  "status_code": 422,
  "message": "Validation error",
  "errors": [
    {
      "field": "price",
      "message": "ensure this value is greater than 0",
      "type": "value_error.number.not_gt"
    }
  ]
}
```

#### Not Found (404)
```json
{
  "success": false,
  "status_code": 404,
  "message": "Product not found: 123e4567-e89b-12d3-a456-**********00"
}
```

#### Conflict (409)
```json
{
  "success": false,
  "status_code": 409,
  "message": "Product with this SKU already exists",
  "errors": {
    "sku": "SKU must be unique"
  }
}
```

## Usage Examples

### cURL Examples

#### Create a Product
```bash
curl -X POST "http://localhost:8000/api/v1/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Gaming Mouse",
    "sku": "GM-001",
    "description": "High-precision gaming mouse",
    "price": 49.99,
    "inventory_level": 25,
    "category": "Electronics",
    "brand": "GameTech"
  }'
```

#### Get Products with Filtering
```bash
curl "http://localhost:8000/api/v1/products?category=Electronics&min_price=20&max_price=100&sort_by=price&sort_order=asc"
```

#### Update a Product
```bash
curl -X PATCH "http://localhost:8000/api/v1/products/123e4567-e89b-12d3-a456-**********00" \
  -H "Content-Type: application/json" \
  -d '{
    "price": 44.99,
    "inventory_level": 30
  }'
```

### Python Examples

```python
import requests

# Base URL
base_url = "http://localhost:8000/api/v1"

# Create a product
product_data = {
    "name": "Bluetooth Speaker",
    "sku": "BS-001",
    "price": 79.99,
    "inventory_level": 15,
    "category": "Electronics"
}

response = requests.post(f"{base_url}/products", json=product_data)
if response.status_code == 201:
    product = response.json()["data"]
    print(f"Created product: {product['id']}")

# Get products
response = requests.get(f"{base_url}/products?category=Electronics")
if response.status_code == 200:
    products = response.json()["data"]["items"]
    print(f"Found {len(products)} products")
```

## Interactive Documentation

The API provides interactive documentation using Swagger UI and ReDoc:

- **Swagger UI**: http://localhost:8000/api/v1/docs
- **ReDoc**: http://localhost:8000/api/v1/redoc

These interfaces allow you to:
- Explore all available endpoints
- Test API calls directly from the browser
- View request/response schemas
- Download OpenAPI specification

## Rate Limiting

Currently, no rate limiting is implemented. This can be added in future versions using middleware.

## Versioning

The API uses URL path versioning (`/api/v1/`). Future versions will be available at `/api/v2/`, etc.

## Support

For API support and questions:
1. Check the interactive documentation
2. Review this documentation
3. Test endpoints using the provided test script
4. Check application logs for detailed error information
